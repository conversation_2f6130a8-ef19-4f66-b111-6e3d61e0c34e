<template>
  <div class="homeLeft">
    <div class="wenwen-left" :class="!showMenu && 'collapse'">
      <wenwen-left-tab ref="wenwenleftTab"
        @wenClickChart="wenClickChart" @sendChatContent="sendChatContent" @getSettingData="getSettingData">
      </wenwen-left-tab>
    </div>
    <div class="wenwen-right" :class="!showMenu && 'collapse'">
      <div class="wenwen-collapse" @click="showMenu = false" v-if="showMenu">
        <i class="iconfont ic-arrow-left"  style="color:  #CF1A1C"></i>
        <span>工具栏</span>
      </div>
      <div class="wenwen-collapse" @click="showMenu = true" v-if="!showMenu">
        <i class="iconfont ic-arrow-right" style="color:  #CF1A1C"></i>
        <span>工具栏</span>
      </div>
      <div class="wenwen-feedback" @click="handleFeedback">
        <div class="feedback-icon-wrapper">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="feedback-icon">
            <path d="M13.125 6.25C13.4702 6.25 13.75 6.52982 13.75 6.875C13.75 7.22018 13.4702 7.5 13.125 7.5H6.875C6.52982 7.5 6.25 7.22018 6.25 6.875C6.25 6.52982 6.52982 6.25 6.875 6.25H13.125Z" fill="currentColor"/>
            <path d="M8.75 9.0625C9.09518 9.0625 9.375 9.34232 9.375 9.6875C9.375 10.0327 9.09518 10.3125 8.75 10.3125H6.875C6.52982 10.3125 6.25 10.0327 6.25 9.6875C6.25 9.34232 6.52982 9.0625 6.875 9.0625H8.75Z" fill="currentColor"/>
            <path d="M11.25 13.75C11.5952 13.75 11.875 14.0298 11.875 14.375C11.875 14.7202 11.5952 15 11.25 15H10.9375C10.5923 15 10.3125 14.7202 10.3125 14.375C10.3125 14.0298 10.5923 13.75 10.9375 13.75H11.25Z" fill="currentColor"/>
            <path d="M13.75 13.75C14.0952 13.75 14.375 14.0298 14.375 14.375C14.375 14.7202 14.0952 15 13.75 15H13.4375C13.0923 15 12.8125 14.7202 12.8125 14.375C12.8125 14.0298 13.0923 13.75 13.4375 13.75H13.75Z" fill="currentColor"/>
            <path d="M16.25 13.75C16.5952 13.75 16.875 14.0298 16.875 14.375C16.875 14.7202 16.5952 15 16.25 15H15.9375C15.5923 15 15.3125 14.7202 15.3125 14.375C15.3125 14.0298 15.5923 13.75 15.9375 13.75H16.25Z" fill="currentColor"/>
            <path d="M15.625 9.06647V3.75H4.375V16.25H6.25C6.59518 16.25 6.875 16.5298 6.875 16.875C6.875 17.2202 6.59518 17.5 6.25 17.5H4.375C3.70624 17.5 3.16004 16.9748 3.12653 16.3144L3.125 16.25V3.75C3.125 3.05964 3.68464 2.5 4.375 2.5H15.625C16.3154 2.5 16.875 3.05964 16.875 3.75V9.06647C16.875 9.41165 16.5952 9.69147 16.25 9.69147C15.9048 9.69147 15.625 9.41165 15.625 9.06647Z" fill="currentColor"/>
            <path d="M13.75 4.0625C13.75 4.40768 13.4702 4.6875 13.125 4.6875C12.7798 4.6875 12.5 4.40768 12.5 4.0625V2.1875C12.5 1.84232 12.7798 1.5625 13.125 1.5625C13.4702 1.5625 13.75 1.84232 13.75 2.1875V4.0625Z" fill="currentColor"/>
            <path d="M7.5 4.0625C7.5 4.40768 7.22018 4.6875 6.875 4.6875C6.52982 4.6875 6.25 4.40768 6.25 4.0625V2.1875C6.25 1.84232 6.52982 1.5625 6.875 1.5625C7.22018 1.5625 7.5 1.84232 7.5 2.1875V4.0625Z" fill="currentColor"/>
            <path d="M12.8125 17.6572L14.4534 16.6919C14.5975 16.6071 14.7617 16.5625 14.9289 16.5625H17.6562V12.5H9.6875V16.5625H11.875C12.3928 16.5625 12.8125 16.9822 12.8125 17.5V17.6572ZM18.9062 16.5625C18.9062 17.2529 18.3466 17.8125 17.6562 17.8125H15.014L12.9755 19.0115C12.3505 19.3791 11.5626 18.9287 11.5625 18.2037V17.8125H9.6875C8.99714 17.8125 8.4375 17.2529 8.4375 16.5625V12.5C8.4375 11.8096 8.99714 11.25 9.6875 11.25H17.6562C18.3466 11.25 18.9062 11.8096 18.9062 12.5V16.5625Z" fill="currentColor"/>
          </svg>
          <span v-if="replyNewCount > 0" class="feedback-badge">{{ replyNewCount }}</span>
        </div>
        <span class="feedback-text">反馈答复<span v-if="replyNewCount > 0"> {{ replyNewCount }}</span></span>
      </div>
        <wenwen-home-index ref="wenwenhomeIndex" :settingParam="settingParam"  @getChatData="getChatData" @editChatSure="editChatSure" @getCreateChat="getCreateChat"  @getKeywordNum="getKeywordNum"></wenwen-home-index>
     </div>
    <!-- 反馈答复Dialog -->
    <feedback-reply-dialog
        v-if="feedbackReplyDialogVisible"
        :visible="feedbackReplyDialogVisible"
        @close="closeFeedbackReplyDialog"
    />
  </div>
</template>
<script>
import WenwenHomeIndex from "@/views/compliance/wenwen/wenwenHomeIndex.vue";
import WenwenLeftTab from "@/views/compliance/wenwen/wenwenLeft-tab.vue";
import FeedbackReplyDialog from "@/views/compliance/wenwen/feedbackReplyDialog.vue";
import {_getIsReplyNewCount} from "@/api/chat";

export default {
  data () {
    return {
      historyParam: {
        currentChat: '', // 当前会话id
        chatList: [], // 聊天会话列表
        chatListNum: 0,
        belongsPlate: '',
        companyCode: '',
        userNamePinYin: '',
        keywordAll: 0,
        keywordNum: 0,
        keyword: '',
        plusStatus: ''
      },
      settingParam: {
        fact: '', // 是否根据事实回答选则
        continuousChat: '', // 是否连续会话（默认否；0 是 ；1 否）
        temperature: '',
        promptId: '',
        promptName: '',
        prompt: '',
        userNamePinYin: '',
        chatModel: '' // 聊天模式（1：日常问答，2：专业模式）
      },
      showMenu: false,
      feedbackReplyDialogVisible: false,
      replyNewCount: 0 // 答复new标识数量
    }
  },
  components: { WenwenLeftTab, WenwenHomeIndex, FeedbackReplyDialog },
  computed: {},
  methods: {
    wenClickChart (data) {
      this.historyParam = data;
      this.$refs.wenwenhomeIndex.currentChat = data.currentChat;
      this.$refs.wenwenhomeIndex.chatList = data.chatList;
      this.$refs.wenwenhomeIndex.chatListNum = data.chatListNum;
      if (data.setBelongsPlateFlag === '1') {
        this.$refs.wenwenhomeIndex.belongsPlate = data.belongsPlate;
      }
      this.$refs.wenwenhomeIndex.companyCode = data.companyCode;
      this.$refs.wenwenhomeIndex.userNamePinYin = data.userNamePinYin;
      this.$refs.wenwenhomeIndex.plusStatus = data.plusStatus;
      this.$refs.wenwenhomeIndex.keywordQuery = data.keyword; // 清除当前对话框标红关键词
    },
    getSettingData () {
      this.settingParam = this.$refs.wenwenleftTab.settingParam;
    },
    getCreateChat (chatName, name, type, index) {
      this.$refs.wenwenleftTab.getCreateChat(chatName, name, type, index);
    },
    sendChatContent (name) {
      this.$refs.wenwenhomeIndex.sendChatContent(name);
    },
    getKeywordNum (indexAll, indexNum) {
      this.historyParam.keywordAll = indexAll;
      this.historyParam.keywordNum = indexNum;
      this.$refs.wenwenleftTab.historyParam.keywordAll = indexAll;
      this.$refs.wenwenleftTab.historyParam.keywordNum = indexNum;
    },
    getChatData (val) {
      this.$refs.wenwenleftTab.getChatData(val);
    },
    editChatSure (name) {
      this.$refs.wenwenleftTab.editChatSure(name);
    },
    handleFeedback () {
      // 打开反馈答复Dialog
      this.feedbackReplyDialogVisible = true;
    },
    closeFeedbackReplyDialog () {
      this.feedbackReplyDialogVisible = false;
      // 关闭弹窗后重新获取数量
      this.getReplyNewCount();
    },
    // 获取答复new标识数量
    async getReplyNewCount () {
      _getIsReplyNewCount().then(res => {
        if (res.data.success) {
          this.replyNewCount = res.data.result;
        }
      })
    }
  },
  mounted () {
    // 页面加载时获取答复new标识数量
    this.getReplyNewCount();
  },
  beforeDestroy () {},
  created () {
  }
}
</script>

<style  scoped lang="scss">
.homeLeft{
    display:flex;
    height: calc(100% - 49px - 60px);
    width: 100%;
    background-color: #fff;
    background-image: url('../../../assets/images/wenwen/background.svg');
    background-size:cover;
    background-repeat: no-repeat;
    position:relative;
    background-position: center;
    .wenwen-left{
      width:300px;
      position:relative;
      background: rgba(252, 254, 255, 0.6);
      transition: all 0.3s;
      &.collapse{
        transform: translateX(-300px - 72px);
      }
    }
    .wenwen-right{
      flex:1;
      position:relative;
      display:flex;
      flex-direction: column;
      width:calc(100% - 72px);
      transition: all 0.3s;
      &.collapse{
        margin-left:-300px;
      }
      .wenwen-collapse{
        position:absolute;
        z-index: 999;
        background-image:url('../../../assets/images/wenwen/btn.png');
        top:40px;
        background-repeat: no-repeat;
        height:100px;
        width:26px;
        cursor: pointer;
        text-align: center;
        font-size: 14px;
        padding-top: 22px;
        line-height: 1.2;
        color: #CF1A1C;
        background-size: 100% 100%;
        .iconfont {
          font-size:14px;
        }
        &:hover {
          color: #CF1A1C;
          .iconfont {
            color: #CF1A1C;
          }
        }
      }
      .wenwen-feedback{
        position:absolute;
        z-index: 999;
        top:160px;
        cursor: pointer;
        color: #CF1A1C;
        padding: 0 0 0 5px;
        display: flex;
        align-items: center;
        width: 32px;
        height: 32px;
        overflow: hidden;
        transition: all 0.3s ease;

        .feedback-icon-wrapper {
          position: relative;
          flex-shrink: 0;

          .feedback-icon {
            color: #CF1A1C;
            transition: color 0.3s ease;
          }

          .iconfont {
            font-size: 16px;
            color: #CF1A1C;
          }

          .feedback-badge {
            position: absolute;
            top: -6px;
            right: -6px;
            background: #FF4D4F;
            color: white;
            border-radius: 50%;
            min-width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 4px;
            box-sizing: border-box;
            border: 1px solid white;
            transition: all 0.3s ease;
            transform: scale(1);
          }
        }

        .feedback-text {
          font-size: 14px;
          color: #CF1A1C;
          margin-left: 6px;
          white-space: nowrap;
          opacity: 0;
          transform: translateX(-10px);
          transition: all 0.3s ease;
        }

        &:hover {
          width: auto;
          min-width: 101px;
          background-color: #CF1A1C;
          color: white;
          clip-path: polygon(0% 0%, 75% 0%, 100% 25%, 100% 75%, 75% 100%, 0% 100%);

          .feedback-icon-wrapper {
            .feedback-icon {
              color: white;
            }

            .feedback-badge {
              opacity: 0;
              transform: scale(0.8);
            }
          }

          .feedback-text {
            opacity: 1;
            transform: translateX(0);
            color: white;
          }
        }
      }
    }
    .wenwen-expand{
      position:absolute;
      top:40%;
      left:0;
      text-align: center;
      cursor: pointer;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      z-index:999;
      &:hover {
        .iconfont {
          color: #CF1A1C;
        }
      }
    }
}
</style>
